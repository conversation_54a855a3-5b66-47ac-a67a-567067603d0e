{"ID": null, "c_name": "B站网页授权链接生成工具", "description": "Generate Bilibili web authorization link and automatically open browser for QR code authorization", "descriptionChinese": "生成B站网页授权链接，自动打开浏览器进行扫码授权", "fullName": "mcp-bilibili--bilibili_web_authorize_link", "inputSchema": {"type": "object", "required": [], "properties": {}}, "is_single_call": 1, "keywords": "B站,授权,<PERSON><PERSON><PERSON>,扫码登录,浏览器授权", "multiFileType": 0, "name": "bilibili_web_authorize_link", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": [], "shouldExclude": false, "excludeReason": ""}