{"ID": null, "c_name": "B站本地Token检查工具", "description": "Check if local cached Bilibili access token is valid to avoid repeated authorization", "descriptionChinese": "检查本地缓存的B站访问令牌是否有效，避免重复授权", "fullName": "mcp-bilibili--bilibili_check_local_token", "inputSchema": {"type": "object", "required": [], "properties": {}}, "is_single_call": 1, "keywords": "B站,token,令牌检查,本地缓存,授权验证", "multiFileType": 0, "name": "bilibili_check_local_token", "outputSchema": {"type": "object"}, "platform": "all", "points": 2, "projectId": null, "projectUUId": "mcp-bilibili", "regex": null, "supportedExtensions": [], "canProcessDirectory": false, "canDirectExecute": true, "isDangerous": false, "canRunIndependently": true, "prerequisiteTools": [], "shouldExclude": false, "excludeReason": ""}