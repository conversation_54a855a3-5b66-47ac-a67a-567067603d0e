#!/usr/bin/env python3
"""
通用MCP工具自动化工作流引擎
支持任意MCP工具集合的动态工作流构建和执行
"""

import json
import asyncio
import logging
from typing import Dict, List, Set, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import networkx as nx
from pathlib import Path

class ExecutionMode(Enum):
    """执行模式"""
    SEQUENTIAL = "sequential"  # 串行执行
    PARALLEL = "parallel"     # 并行执行
    CONDITIONAL = "conditional"  # 条件执行

@dataclass
class ToolConfig:
    """工具配置"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    prerequisites: List[str] = field(default_factory=list)
    supported_extensions: List[str] = field(default_factory=list)
    can_run_independently: bool = True
    is_dangerous: bool = False
    platform: str = "all"

@dataclass
class ExecutionContext:
    """执行上下文"""
    tool_results: Dict[str, Any] = field(default_factory=dict)
    shared_data: Dict[str, Any] = field(default_factory=dict)
    user_inputs: Dict[str, Any] = field(default_factory=dict)
    execution_history: List[str] = field(default_factory=list)
    current_step: int = 0
    total_steps: int = 0

class UniversalMCPWorkflowEngine:
    """通用MCP工作流引擎"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.tools: Dict[str, ToolConfig] = {}
        self.dependency_graph = nx.DiGraph()
        self.execution_plans: Dict[str, List[str]] = {}
        self.logger = self._setup_logger()
        self.mcp_client = None  # 实际的MCP客户端
        
        if config_path:
            self.load_tools_config(config_path)
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('UniversalMCPWorkflow')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def load_tools_config(self, config_path: str):
        """加载工具配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            tools_data = json.load(f)
        
        # 解析工具配置
        for tool_data in tools_data:
            tool = ToolConfig(
                name=tool_data['name'],
                description=tool_data.get('descriptionChinese', tool_data.get('description', '')),
                input_schema=tool_data.get('inputSchema', {}),
                output_schema=tool_data.get('outputSchema', {}),
                prerequisites=tool_data.get('prerequisiteTools', []),
                supported_extensions=tool_data.get('supportedExtensions', []),
                can_run_independently=tool_data.get('canRunIndependently', True),
                is_dangerous=tool_data.get('isDangerous', False),
                platform=tool_data.get('platform', 'all')
            )
            self.tools[tool.name] = tool
        
        # 构建依赖图
        self._build_dependency_graph()
        self.logger.info(f"加载了 {len(self.tools)} 个工具")
    
    def _build_dependency_graph(self):
        """构建依赖关系图"""
        self.dependency_graph.clear()
        
        # 添加所有工具作为节点
        for tool_name in self.tools:
            self.dependency_graph.add_node(tool_name)
        
        # 添加依赖关系作为边
        for tool_name, tool in self.tools.items():
            for prerequisite in tool.prerequisites:
                if prerequisite in self.tools:
                    self.dependency_graph.add_edge(prerequisite, tool_name)
        
        # 检查循环依赖
        if not nx.is_directed_acyclic_graph(self.dependency_graph):
            cycles = list(nx.simple_cycles(self.dependency_graph))
            raise ValueError(f"检测到循环依赖: {cycles}")
    
    def analyze_execution_paths(self, target_tools: List[str]) -> Dict[str, List[str]]:
        """分析执行路径"""
        execution_paths = {}
        
        for target in target_tools:
            if target not in self.tools:
                raise ValueError(f"未知工具: {target}")
            
            # 找到到达目标工具的所有前置依赖
            predecessors = list(nx.ancestors(self.dependency_graph, target))
            predecessors.append(target)
            
            # 拓扑排序确定执行顺序
            subgraph = self.dependency_graph.subgraph(predecessors)
            execution_order = list(nx.topological_sort(subgraph))
            
            execution_paths[target] = execution_order
        
        return execution_paths
    
    def generate_execution_plan(self, goal: str, user_inputs: Dict[str, Any] = None) -> List[str]:
        """生成执行计划"""
        user_inputs = user_inputs or {}
        
        # 根据目标分析需要执行的工具
        target_tools = self._analyze_goal(goal, user_inputs)
        
        # 获取执行路径
        paths = self.analyze_execution_paths(target_tools)
        
        # 合并路径，去重并保持拓扑顺序
        all_tools = set()
        for path in paths.values():
            all_tools.update(path)
        
        # 对所有涉及的工具进行拓扑排序
        subgraph = self.dependency_graph.subgraph(all_tools)
        execution_plan = list(nx.topological_sort(subgraph))
        
        self.logger.info(f"生成执行计划: {execution_plan}")
        return execution_plan
    
    def _analyze_goal(self, goal: str, user_inputs: Dict[str, Any]) -> List[str]:
        """分析目标，确定需要执行的工具"""
        # 这里可以实现更复杂的目标分析逻辑
        # 例如：基于关键词匹配、语义分析等
        
        # 简单实现：基于关键词匹配
        goal_lower = goal.lower()
        target_tools = []
        
        for tool_name, tool in self.tools.items():
            # 检查工具描述中是否包含目标关键词
            if any(keyword in goal_lower for keyword in [
                tool.name.lower(),
                tool.description.lower()
            ]):
                target_tools.append(tool_name)
        
        # 如果没有匹配到具体工具，尝试基于文件扩展名匹配
        if not target_tools and 'file_path' in user_inputs:
            file_path = user_inputs['file_path']
            file_ext = Path(file_path).suffix.lower().lstrip('.')
            
            for tool_name, tool in self.tools.items():
                if file_ext in tool.supported_extensions:
                    target_tools.append(tool_name)
        
        if not target_tools:
            # 如果仍然没有匹配，返回所有可独立运行的工具
            target_tools = [
                tool_name for tool_name, tool in self.tools.items()
                if tool.can_run_independently
            ]
        
        return target_tools
    
    async def execute_workflow(self, goal: str, user_inputs: Dict[str, Any] = None,
                             execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL) -> Dict[str, Any]:
        """执行工作流"""
        user_inputs = user_inputs or {}
        context = ExecutionContext(user_inputs=user_inputs)
        
        try:
            # 生成执行计划
            execution_plan = self.generate_execution_plan(goal, user_inputs)
            context.total_steps = len(execution_plan)
            
            # 执行计划
            for tool_name in execution_plan:
                context.current_step += 1
                self.logger.info(f"执行步骤 {context.current_step}/{context.total_steps}: {tool_name}")
                
                # 准备工具参数
                tool_params = await self._prepare_tool_params(tool_name, context)
                
                # 执行工具
                result = await self._execute_tool(tool_name, tool_params, context)
                
                # 处理结果
                context.tool_results[tool_name] = result
                context.execution_history.append(tool_name)
                
                # 更新共享数据
                await self._update_shared_data(tool_name, result, context)
            
            return {
                "success": True,
                "results": context.tool_results,
                "execution_history": context.execution_history,
                "shared_data": context.shared_data
            }
            
        except Exception as e:
            self.logger.error(f"工作流执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "partial_results": context.tool_results,
                "execution_history": context.execution_history
            }
    
    async def _prepare_tool_params(self, tool_name: str, context: ExecutionContext) -> Dict[str, Any]:
        """准备工具参数"""
        tool = self.tools[tool_name]
        params = {}
        
        # 从输入模式获取必需参数
        required_params = tool.input_schema.get('required', [])
        properties = tool.input_schema.get('properties', {})
        
        for param_name in required_params:
            param_config = properties.get(param_name, {})
            
            # 尝试从不同来源获取参数值
            param_value = None
            
            # 1. 从用户输入获取
            if param_name in context.user_inputs:
                param_value = context.user_inputs[param_name]
            
            # 2. 从共享数据获取
            elif param_name in context.shared_data:
                param_value = context.shared_data[param_name]
            
            # 3. 从之前工具的结果获取
            elif param_name in ['access_token', 'upload_token']:
                # 特殊处理常见的令牌参数
                for prev_tool, result in context.tool_results.items():
                    if isinstance(result, dict) and param_name in result:
                        param_value = result[param_name]
                        break
            
            # 4. 使用默认值
            elif 'default' in param_config:
                param_value = param_config['default']
            
            # 5. 请求用户输入
            else:
                param_value = await self._request_user_input(param_name, param_config)
            
            if param_value is not None:
                params[param_name] = param_value
            else:
                raise ValueError(f"无法获取工具 {tool_name} 的必需参数: {param_name}")
        
        return params
    
    async def _execute_tool(self, tool_name: str, params: Dict[str, Any], 
                          context: ExecutionContext) -> Dict[str, Any]:
        """执行工具"""
        tool = self.tools[tool_name]
        
        # 检查危险操作
        if tool.is_dangerous:
            confirm = input(f"工具 {tool_name} 可能执行危险操作，是否继续？(y/N): ")
            if confirm.lower() != 'y':
                raise ValueError(f"用户取消执行危险工具: {tool_name}")
        
        # 这里应该调用实际的MCP客户端
        # result = await self.mcp_client.call_tool(tool_name, params)
        
        # 模拟执行结果
        self.logger.info(f"执行工具: {tool_name}, 参数: {params}")
        return {"success": True, "data": {}, "tool": tool_name, "params": params}
    
    async def _update_shared_data(self, tool_name: str, result: Dict[str, Any], 
                                context: ExecutionContext):
        """更新共享数据"""
        # 提取常见的输出数据到共享数据中
        if isinstance(result, dict) and 'data' in result:
            data = result['data']
            
            # 提取常见字段
            common_fields = ['access_token', 'upload_token', 'resource_id', 'url', 'state']
            for field in common_fields:
                if field in data:
                    context.shared_data[field] = data[field]
    
    async def _request_user_input(self, param_name: str, param_config: Dict[str, Any]) -> Any:
        """请求用户输入"""
        description = param_config.get('description', param_name)
        param_type = param_config.get('type', 'string')
        
        prompt = f"请输入 {param_name} ({description}): "
        
        user_input = input(prompt)
        
        # 类型转换
        if param_type == 'integer':
            return int(user_input)
        elif param_type == 'number':
            return float(user_input)
        elif param_type == 'boolean':
            return user_input.lower() in ['true', 'yes', 'y', '1']
        else:
            return user_input
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return [
            {
                "name": tool.name,
                "description": tool.description,
                "prerequisites": tool.prerequisites,
                "can_run_independently": tool.can_run_independently,
                "is_dangerous": tool.is_dangerous,
                "supported_extensions": tool.supported_extensions
            }
            for tool in self.tools.values()
        ]
    
    def visualize_dependency_graph(self) -> str:
        """可视化依赖关系图"""
        # 生成Mermaid图表代码
        lines = ["graph TD"]
        
        for tool_name in self.tools:
            # 简化工具名称用于显示
            display_name = tool_name.replace('_', '<br/>')
            lines.append(f'    {tool_name}["{display_name}"]')
        
        for edge in self.dependency_graph.edges():
            lines.append(f'    {edge[0]} --> {edge[1]}')
        
        return '\n'.join(lines)

# 使用示例
async def main():
    # 初始化引擎
    engine = UniversalMCPWorkflowEngine("mcp-bilibili-tools.json")
    
    # 查看可用工具
    tools = engine.get_available_tools()
    print(f"可用工具数量: {len(tools)}")
    
    # 执行工作流
    result = await engine.execute_workflow(
        goal="上传视频到B站",
        user_inputs={
            "video_file_path": "/path/to/video.mp4",
            "cover_file_path": "/path/to/cover.jpg",
            "title": "我的视频",
            "description": "视频描述",
            "tags": "标签1,标签2"
        }
    )
    
    print(f"执行结果: {result}")

class PromptTemplateGenerator:
    """通用提示词模板生成器"""

    def __init__(self, tools_config: Dict[str, ToolConfig]):
        self.tools = tools_config

    def generate_workflow_prompt(self, goal: str, execution_plan: List[str],
                                context: ExecutionContext) -> str:
        """生成工作流执行提示词"""
        prompt_parts = [
            f"🎯 **目标**: {goal}",
            f"📋 **执行计划**: 共{len(execution_plan)}个步骤",
            ""
        ]

        for i, tool_name in enumerate(execution_plan, 1):
            tool = self.tools[tool_name]
            status = "✅" if tool_name in context.execution_history else "⏳"
            prompt_parts.append(f"{status} **步骤{i}**: {tool.description}")

        prompt_parts.extend([
            "",
            f"🔄 **当前进度**: {context.current_step}/{context.total_steps}",
            f"📊 **执行历史**: {' → '.join(context.execution_history[-3:])}" if context.execution_history else "📊 **执行历史**: 暂无"
        ])

        return "\n".join(prompt_parts)

    def generate_tool_prompt(self, tool_name: str, params: Dict[str, Any]) -> str:
        """生成单个工具执行提示词"""
        tool = self.tools[tool_name]

        prompt_parts = [
            f"🔧 **执行工具**: {tool.description}",
            f"📝 **工具名称**: `{tool_name}`",
            ""
        ]

        if tool.prerequisites:
            prompt_parts.append(f"⚠️ **前置依赖**: {', '.join(tool.prerequisites)}")

        if tool.is_dangerous:
            prompt_parts.append("🚨 **危险操作**: 此操作可能有风险，请确认后继续")

        prompt_parts.extend([
            "",
            "📥 **输入参数**:"
        ])

        for param_name, param_value in params.items():
            prompt_parts.append(f"  - `{param_name}`: {param_value}")

        return "\n".join(prompt_parts)

    def generate_error_prompt(self, tool_name: str, error: str,
                            retry_count: int, max_retries: int) -> str:
        """生成错误处理提示词"""
        tool = self.tools[tool_name]

        prompt_parts = [
            f"❌ **执行失败**: {tool.description}",
            f"🔧 **工具名称**: `{tool_name}`",
            f"📝 **错误信息**: {error}",
            f"🔄 **重试次数**: {retry_count}/{max_retries}",
            ""
        ]

        if retry_count < max_retries:
            prompt_parts.extend([
                "🔄 **自动重试中...**",
                "💡 **建议**: 请检查网络连接和参数设置"
            ])
        else:
            prompt_parts.extend([
                "🛑 **重试次数已达上限**",
                "💡 **建议**: 请检查错误信息并手动处理"
            ])

        return "\n".join(prompt_parts)

    def generate_completion_prompt(self, goal: str, results: Dict[str, Any],
                                 execution_time: float) -> str:
        """生成完成提示词"""
        prompt_parts = [
            f"🎉 **工作流完成**: {goal}",
            f"⏱️ **执行时间**: {execution_time:.2f}秒",
            f"✅ **执行步骤**: {len(results.get('execution_history', []))}个",
            ""
        ]

        if results.get('success'):
            prompt_parts.extend([
                "🎯 **执行结果**: 成功",
                "📊 **输出数据**:"
            ])

            for tool_name, result in results.get('results', {}).items():
                if isinstance(result, dict) and result.get('success'):
                    prompt_parts.append(f"  ✅ {tool_name}: 执行成功")
                else:
                    prompt_parts.append(f"  ❌ {tool_name}: 执行失败")
        else:
            prompt_parts.extend([
                "❌ **执行结果**: 失败",
                f"📝 **错误信息**: {results.get('error', '未知错误')}"
            ])

        return "\n".join(prompt_parts)

if __name__ == "__main__":
    asyncio.run(main())
